# 上海理工大学1100校区羽毛球场预约系统

## 系统功能

### 基本功能
1. **信息录入** - 预约场地
   - 录入预约人员信息（工号/学号，姓名，联系电话）
   - 选择场地和时间进行预约

2. **信息输出** - 查看三天内场地状态
   - 显示未来三天所有场地的预约情况
   - 标明已约、可约、限制时间段

3. **信息查询**
   - 按场地编号查询预约记录
   - 按预约人员手机号查询预约记录

4. **信息修改** - 取消预约
   - 根据场地编号和手机号取消预约

### 新增功能
5. **数据持久化**
   - 所有预约记录自动保存到 `Reserve.txt` 文件
   - 程序启动时自动加载历史预约记录
   - 每次预约或取消操作后自动保存

## 使用规则

### 开放时间
- **周一~周五**: 下午3点~9点
  - 周一、三、五下午5点~8点为教工羽协活动时间（不可预约）
- **周六日**: 上午9点~下午9点

### 场地信息
- 共8片场地（编号1-8）
- 预约单元：1小时
- 仅限学校师生使用

## 编译和运行

```bash
# 编译
gcc -o badminton badminton.c

# 运行
./badminton
```

## 数据文件格式

预约记录保存在 `Reserve.txt` 文件中，采用CSV格式：
```
场地编号,年份,月份,日期,小时,工号/学号,姓名,电话
```

示例：
```
1,2025,1,15,16,2021001,张三,13812345678
2,2025,1,16,10,T001,李老师,13987654321
```

## 修复内容

1. **添加文件保存功能**
   - 新增 `save_reservations_to_file()` 函数：将内存中的预约记录保存到文件
   - 新增 `load_reservations_from_file()` 函数：从文件加载预约记录到内存
   - 程序启动时自动加载历史数据
   - 每次预约成功后自动保存到文件
   - 每次取消预约后自动更新文件

2. **修复输入缓冲问题**
   - 在 `get_user_input_string()` 函数中添加 `fflush(stdout)`
   - 解决"请输入姓名"和"请输入电话"同时显示的问题
   - 确保输入提示按顺序正确显示

## 技术实现细节

### 文件格式
- 使用CSV格式存储数据，便于读取和调试
- 每行包含：场地编号,年份,月份,日期,小时,工号/学号,姓名,电话
- 自动处理文件不存在的情况

### 数据一致性
- 程序启动时加载所有历史记录
- 每次修改操作后立即保存
- 内存和文件数据保持同步

## 使用示例

1. **首次运行**：
   ```
   欢迎使用上海理工大学1100校区羽毛球场预约系统！
   正在加载预约数据...
   提示：数据文件 Reserve.txt 不存在，将创建新文件。
   系统已启动，数据将自动保存在 Reserve.txt 文件中。
   ```

2. **预约成功后**：
   ```
   预约成功！
   预约信息：场地1 - 2025年1月15日 16:00 - 张三
   预约记录已保存到文件。
   ```

3. **取消预约后**：
   ```
   预约取消成功！
   预约记录已更新到文件。
   ```

## 注意事项

- 程序会在当前目录创建 `Reserve.txt` 文件
- 首次运行时如果文件不存在会自动创建
- 所有预约记录都会持久保存，重启程序后数据不会丢失
- 文件采用UTF-8编码，支持中文字符
- 建议定期备份 `Reserve.txt` 文件
