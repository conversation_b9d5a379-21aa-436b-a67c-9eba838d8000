# 羽毛球场预约系统

## 项目简介
上海理工大学1100校区羽毛球场预约系统，使用C语言开发，符合大一学生编程水平。

## 功能特点
1. **信息录入** - 预约场地
2. **信息输出** - 查看三天内场地状态  
3. **信息查询** - 按场地或手机号查询
4. **信息修改** - 取消预约

## 开放时间规则
- **工作日（周一至周五）**：15:00-21:00
- **限制时间**：周一、三、五 17:00-20:00为教工羽协活动时间
- **周末（周六、周日）**：9:00-21:00

## 编译和运行

### 方法1：使用批处理文件（推荐）
```bash
双击 run_simple.bat
```

### 方法2：手动编译
```bash
# 设置中文编码
chcp 936

# 编译程序
gcc -o badminton_simple badminton_simple.c

# 运行程序
badminton_simple.exe
```

### 方法3：使用Dev-C++或Code::Blocks
1. 打开 `badminton_simple.c` 文件
2. 点击编译并运行
3. 如果出现乱码，在IDE中设置编码为GBK/GB2312

## 解决中文乱码问题

### Windows系统
1. **命令行方法**：
   ```cmd
   chcp 936
   badminton_simple.exe
   ```

2. **IDE设置**：
   - Dev-C++：工具 → 编辑器选项 → 代码 → 默认编码 → GBK
   - Code::Blocks：Settings → Editor → General Settings → Encoding → System default

3. **系统设置**：
   - 控制面板 → 区域 → 管理 → 更改系统区域设置 → 勾选"Beta版：使用Unicode UTF-8提供全球语言支持"

## 文件说明
- `badminton_simple.c` - 主程序文件
- `Reserve.txt` - 预约数据存储文件（自动生成）
- `run_simple.bat` - 一键编译运行脚本
- `README.md` - 项目说明文档

## 代码结构
```
badminton_simple.c
├── 数据结构定义
│   ├── User结构体（用户信息）
│   └── Node结构体（预约记录）
├── 核心函数
│   ├── check_time() - 时间检查
│   ├── add_node() - 添加预约
│   ├── save_file() - 保存文件
│   ├── load_file() - 加载文件
│   ├── add_booking() - 预约录入
│   ├── show_status() - 状态显示
│   ├── search_info() - 信息查询
│   └── cancel_booking() - 取消预约
└── main() - 主程序
```

## 团队分工建议
- **成员1**：数据结构 + 时间检查函数
- **成员2**：链表操作 + 文件操作  
- **成员3**：预约录入 + 场地状态显示
- **成员4**：查询功能 + 取消预约
- **成员5**：主程序 + 系统集成测试

## 使用说明
1. 启动程序后会自动加载历史预约数据
2. 根据菜单提示选择相应功能
3. 按照提示输入相关信息
4. 所有预约数据会自动保存到Reserve.txt文件

## 注意事项
- 确保输入的时间在开放时间范围内
- 场地编号范围：1-8
- 手机号用于识别预约记录，请准确输入
- 程序会自动检查时间冲突

## 常见问题
**Q: 程序显示乱码怎么办？**
A: 使用run_simple.bat运行，或在命令行执行chcp 936设置编码

**Q: 编译失败怎么办？**  
A: 确保安装了GCC编译器，或使用Dev-C++等IDE

**Q: 预约数据丢失怎么办？**
A: 检查Reserve.txt文件是否存在，程序会自动保存数据

## 开发环境
- 编程语言：C语言
- 编译器：GCC 或 Dev-C++
- 操作系统：Windows
- 编码格式：GBK/GB2312
