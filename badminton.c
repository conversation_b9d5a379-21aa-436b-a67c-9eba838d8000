#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>

// 常量定义
#define MAX_COURTS 8                    // 场地总数
#define MAX_ID_LEN 10                   // 工号/学号最大长度
#define MAX_NAME_LEN 50                 // 姓名最大长度
#define MAX_PHONE_LEN 15                // 电话号码最大长度
#define WEEKDAY_START_HOUR 15           // 工作日开始时间
#define WEEKDAY_END_HOUR 21             // 工作日结束时间
#define WEEKEND_START_HOUR 9            // 周末开始时间
#define WEEKEND_END_HOUR 21             // 周末结束时间
#define RESTRICTED_START_HOUR 17        // 限制时间开始
#define RESTRICTED_END_HOUR 20          // 限制时间结束
#define FILENAME "Reserve.txt"          // 数据文件名

// 用户信息结构体
typedef struct {
    char id[MAX_ID_LEN];                // 工号/学号
    char name[MAX_NAME_LEN];            // 姓名
    char phone[MAX_PHONE_LEN];          // 联系电话
} User;

// 预约记录结构体
typedef struct Reservation {
    int court_id;                       // 场地编号(1-8)
    int year;                           // 年份
    int month;                          // 月份
    int day;                            // 日期
    int hour;                           // 小时(24小时制)
    User user;                          // 预约用户信息
    struct Reservation* next;           // 链表指针
} Reservation;

// 全局变量
Reservation* reservation_head = NULL;   // 预约记录链表头指针

// 获取当前系统时间
void get_current_time(int* year, int* month, int* day, int* hour) {
    time_t rawtime;
    struct tm* timeinfo;
    time(&rawtime);
    timeinfo = localtime(&rawtime);
    *year = timeinfo->tm_year + 1900;
    *month = timeinfo->tm_mon + 1;
    *day = timeinfo->tm_mday;
    *hour = timeinfo->tm_hour;
}

// 判断是否为闰年
int is_leap_year(int year) {
    return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
}

// 获取某月的天数
int get_days_in_month(int year, int month) {
    int days[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
    if (month == 2 && is_leap_year(year)) return 29;
    return days[month - 1];
}

// 验证日期是否有效
int is_valid_date(int year, int month, int day) {
    if (year < 1900 || year > 2100) return 0;
    if (month < 1 || month > 12) return 0;
    if (day < 1 || day > get_days_in_month(year, month)) return 0;
    return 1;
}

// 计算星期几 (1=周一, 2=周二, ..., 7=周日)
int get_weekday(int year, int month, int day) {
    if (!is_valid_date(year, month, day)) return -1;
    if (month < 3) { month += 12; year--; }
    int k = year % 100, j = year / 100;
    int h = (day + (13 * (month + 1)) / 5 + k + k / 4 + j / 4 - 2 * j) % 7;
    return (h + 5) % 7 + 1;
}

// 检查是否为未来时间
int is_future_time(int year, int month, int day, int hour) {
    int curr_year, curr_month, curr_day, curr_hour;
    get_current_time(&curr_year, &curr_month, &curr_day, &curr_hour);
    if (year > curr_year) return 1;
    if (year < curr_year) return 0;
    if (month > curr_month) return 1;
    if (month < curr_month) return 0;
    if (day > curr_day) return 1;
    if (day < curr_day) return 0;
    return hour > curr_hour;
}

// 检查场地在指定时间是否开放
int is_court_available_time(int weekday, int hour) {
    if (weekday >= 1 && weekday <= 5) {
        if (hour < WEEKDAY_START_HOUR || hour >= WEEKDAY_END_HOUR) return 0;
        if ((weekday == 1 || weekday == 3 || weekday == 5) &&
            hour >= RESTRICTED_START_HOUR && hour < RESTRICTED_END_HOUR) return 0;
        return 1;
    }
    if (weekday == 6 || weekday == 7) {
        return (hour >= WEEKEND_START_HOUR && hour < WEEKEND_END_HOUR);
    }
    return 0;
}

// 验证预约时间的完整有效性
int is_valid_time(int year, int month, int day, int hour) {
    if (!is_valid_date(year, month, day)) return 0;
    if (hour < 0 || hour >= 24) return 0;
    if (!is_future_time(year, month, day, hour)) return 0;
    int weekday = get_weekday(year, month, day);
    if (weekday == -1) return 0;
    return is_court_available_time(weekday, hour);
}

// 比较两个时间的先后顺序
int compare_time(int year1, int month1, int day1, int hour1,
                 int year2, int month2, int day2, int hour2) {
    if (year1 != year2) return year1 - year2;
    if (month1 != month2) return month1 - month2;
    if (day1 != day2) return day1 - day2;
    return hour1 - hour2;
}

// 检查时间冲突
int check_conflict(Reservation* head, int court_id, int year, int month, int day, int hour) {
    Reservation* current = head;
    while (current != NULL) {
        if (current->court_id == court_id && current->year == year &&
            current->month == month && current->day == day && current->hour == hour) {
            return 1;
        }
        current = current->next;
    }
    return 0;
}

// 创建新的预约节点
Reservation* create_reservation_node(int court_id, int year, int month, int day, int hour, User user) {
    Reservation* new_node = (Reservation*)malloc(sizeof(Reservation));
    if (new_node == NULL) return NULL;
    new_node->court_id = court_id;
    new_node->year = year;
    new_node->month = month;
    new_node->day = day;
    new_node->hour = hour;
    new_node->user = user;
    new_node->next = NULL;
    return new_node;
}

// 按时间顺序插入预约记录
int add_reservation(Reservation** head, int court_id, int year, int month, int day, int hour, User user) {
    if (!is_valid_time(year, month, day, hour)) return -1;
    if (court_id < 1 || court_id > MAX_COURTS) return -2;
    if (check_conflict(*head, court_id, year, month, day, hour)) return -3;

    Reservation* new_node = create_reservation_node(court_id, year, month, day, hour, user);
    if (new_node == NULL) return -4;

    if (*head == NULL || compare_time(year, month, day, hour,
                                      (*head)->year, (*head)->month, (*head)->day, (*head)->hour) < 0) {
        new_node->next = *head;
        *head = new_node;
    } else {
        Reservation* current = *head;
        while (current->next != NULL &&
               compare_time(year, month, day, hour,
                           current->next->year, current->next->month,
                           current->next->day, current->next->hour) > 0) {
            current = current->next;
        }
        new_node->next = current->next;
        current->next = new_node;
    }
    return 0;
}

// 取消预约
int cancel_reservation(Reservation** head, int court_id, char* phone) {
    if (*head == NULL || phone == NULL) return -1;

    Reservation* current = *head;
    Reservation* prev = NULL;

    while (current != NULL) {
        if (current->court_id == court_id && strcmp(current->user.phone, phone) == 0) {
            if (prev == NULL) {
                *head = current->next;
            } else {
                prev->next = current->next;
            }
            free(current);
            return 0;
        }
        prev = current;
        current = current->next;
    }
    return -2;
}

// 按场地查询预约记录
void query_by_court(Reservation* head, int court_id) {
    if (court_id < 1 || court_id > MAX_COURTS) {
        printf("场地编号无效。\n");
        return;
    }

    printf("\n场地%d的预约记录：\n", court_id);
    Reservation* current = head;
    int found = 0;

    while (current != NULL) {
        if (current->court_id == court_id) {
            printf("  %d年%d月%d日 %02d:00 - %s(%s) - %s\n",
                   current->year, current->month, current->day, current->hour,
                   current->user.name, current->user.id, current->user.phone);
            found = 1;
        }
        current = current->next;
    }

    if (!found) {
        printf("  暂无预约记录。\n");
    }
}

// 按手机号查询预约记录
void query_by_phone(Reservation* head, char* phone) {
    if (phone == NULL) return;

    printf("\n手机号%s的预约记录：\n", phone);
    Reservation* current = head;
    int found = 0;

    while (current != NULL) {
        if (strcmp(current->user.phone, phone) == 0) {
            printf("  场地%d - %d年%d月%d日 %02d:00 - %s(%s)\n",
                   current->court_id, current->year, current->month, current->day, current->hour,
                   current->user.name, current->user.id);
            found = 1;
        }
        current = current->next;
    }

    if (!found) {
        printf("  暂无预约记录。\n");
    }
}

// 显示三天内场地状态
void display_three_days_status(Reservation* head) {
    int curr_year, curr_month, curr_day, curr_hour;
    get_current_time(&curr_year, &curr_month, &curr_day, &curr_hour);

    printf("\n=== 三天内场地预约状态 ===\n");

    for (int day_offset = 0; day_offset < 3; day_offset++) {
        int check_year = curr_year;
        int check_month = curr_month;
        int check_day = curr_day + day_offset;

        int days_in_month = get_days_in_month(check_year, check_month);
        if (check_day > days_in_month) {
            check_day -= days_in_month;
            check_month++;
            if (check_month > 12) {
                check_month = 1;
                check_year++;
            }
        }

        int weekday = get_weekday(check_year, check_month, check_day);
        printf("\n%d年%d月%d日 (", check_year, check_month, check_day);

        char* weekday_names[] = {"", "周一", "周二", "周三", "周四", "周五", "周六", "周日"};
        printf("%s):\n", weekday_names[weekday]);

        for (int court = 1; court <= MAX_COURTS; court++) {
            printf("场地%d: ", court);

            int start_hour = (weekday >= 1 && weekday <= 5) ? WEEKDAY_START_HOUR : WEEKEND_START_HOUR;
            int end_hour = (weekday >= 1 && weekday <= 5) ? WEEKDAY_END_HOUR : WEEKEND_END_HOUR;

            for (int hour = start_hour; hour < end_hour; hour++) {
                if ((weekday == 1 || weekday == 3 || weekday == 5) &&
                    hour >= RESTRICTED_START_HOUR && hour < RESTRICTED_END_HOUR) {
                    printf("%02d:00[限制] ", hour);
                    continue;
                }

                int is_reserved = check_conflict(head, court, check_year, check_month, check_day, hour);
                printf("%02d:00[%s] ", hour, is_reserved ? "已约" : "可约");
            }
            printf("\n");
        }
    }
    printf("\n");
}

// 保存预约记录到文件
int save_reservations_to_file(Reservation* head) {
    FILE* file = fopen(FILENAME, "w");
    if (file == NULL) {
        printf("错误：无法打开文件 %s 进行写入。\n", FILENAME);
        return -1;
    }

    Reservation* current = head;
    while (current != NULL) {
        fprintf(file, "%d,%d,%d,%d,%d,%s,%s,%s\n",
                current->court_id, current->year, current->month, current->day, current->hour,
                current->user.id, current->user.name, current->user.phone);
        current = current->next;
    }

    fclose(file);
    return 0;
}

// 从文件加载预约记录
int load_reservations_from_file(Reservation** head) {
    FILE* file = fopen(FILENAME, "r");
    if (file == NULL) {
        printf("提示：数据文件 %s 不存在，将创建新文件。\n", FILENAME);
        return 0; // 文件不存在不算错误
    }

    char line[256];
    while (fgets(line, sizeof(line), file) != NULL) {
        int court_id, year, month, day, hour;
        User user;

        // 解析CSV格式的数据
        char* token = strtok(line, ",");
        if (token == NULL) continue;
        court_id = atoi(token);

        token = strtok(NULL, ",");
        if (token == NULL) continue;
        year = atoi(token);

        token = strtok(NULL, ",");
        if (token == NULL) continue;
        month = atoi(token);

        token = strtok(NULL, ",");
        if (token == NULL) continue;
        day = atoi(token);

        token = strtok(NULL, ",");
        if (token == NULL) continue;
        hour = atoi(token);

        token = strtok(NULL, ",");
        if (token == NULL) continue;
        strncpy(user.id, token, MAX_ID_LEN - 1);
        user.id[MAX_ID_LEN - 1] = '\0';

        token = strtok(NULL, ",");
        if (token == NULL) continue;
        strncpy(user.name, token, MAX_NAME_LEN - 1);
        user.name[MAX_NAME_LEN - 1] = '\0';

        token = strtok(NULL, ",\n");
        if (token == NULL) continue;
        strncpy(user.phone, token, MAX_PHONE_LEN - 1);
        user.phone[MAX_PHONE_LEN - 1] = '\0';

        // 创建并插入节点
        Reservation* new_node = create_reservation_node(court_id, year, month, day, hour, user);
        if (new_node != NULL) {
            // 按时间顺序插入
            if (*head == NULL || compare_time(year, month, day, hour,
                                              (*head)->year, (*head)->month, (*head)->day, (*head)->hour) < 0) {
                new_node->next = *head;
                *head = new_node;
            } else {
                Reservation* current = *head;
                while (current->next != NULL &&
                       compare_time(year, month, day, hour,
                                   current->next->year, current->next->month,
                                   current->next->day, current->next->hour) > 0) {
                    current = current->next;
                }
                new_node->next = current->next;
                current->next = new_node;
            }
        }
    }

    fclose(file);
    printf("成功从文件 %s 加载预约记录。\n", FILENAME);
    return 0;
}

// 释放链表内存
void free_reservation_list(Reservation* head) {
    Reservation* current = head;
    while (current != NULL) {
        Reservation* temp = current;
        current = current->next;
        free(temp);
    }
}

// 显示主菜单
void display_menu(void) {
    printf("\n");
    printf("========================================\n");
    printf("  上海理工大学1100校区羽毛球场预约系统  \n");
    printf("========================================\n");
    printf("1. 信息录入 - 预约场地\n");
    printf("2. 信息输出 - 查看三天内场地状态\n");
    printf("3. 信息查询 - 按场地或手机号查询\n");
    printf("4. 信息修改 - 取消预约\n");
    printf("0. 退出系统\n");
    printf("========================================\n");
    printf("请选择功能 (0-4): ");
}

// 获取整数输入
int get_user_input_int(char* prompt, int min, int max) {
    int value;
    char buffer[100];

    while (1) {
        printf("%s", prompt);
        if (fgets(buffer, sizeof(buffer), stdin) != NULL) {
            if (sscanf(buffer, "%d", &value) == 1) {
                if (value >= min && value <= max) {
                    return value;
                }
            }
        }
        printf("输入无效，请输入 %d 到 %d 之间的数字。\n", min, max);
    }
}

// 获取字符串输入
void get_user_input_string(char* prompt, char* buffer, int max_len) {
    printf("%s", prompt);
    fflush(stdout); // 强制刷新输出缓冲区
    if (fgets(buffer, max_len, stdin) != NULL) {
        int len = strlen(buffer);
        if (len > 0 && buffer[len-1] == '\n') {
            buffer[len-1] = '\0';
        }
    }
}

// 验证手机号格式
int validate_phone(char* phone) {
    if (phone == NULL || strlen(phone) < 11) return 0;
    for (int i = 0; phone[i] != '\0'; i++) {
        if (phone[i] < '0' || phone[i] > '9') return 0;
    }
    return 1;
}

// 验证工号/学号格式
int validate_id(char* id) {
    if (id == NULL || strlen(id) < 3) return 0;
    return 1;
}

// 处理添加预约
void handle_add_reservation(Reservation** head) {
    printf("\n=== 预约场地 ===\n");

    User user;
    int court_id, year, month, day, hour;

    // 获取用户信息
    while (1) {
        get_user_input_string("请输入工号/学号: ", user.id, MAX_ID_LEN);
        if (validate_id(user.id)) break;
        printf("工号/学号格式不正确，请重新输入。\n");
    }

    get_user_input_string("请输入姓名: ", user.name, MAX_NAME_LEN);

    while (1) {
        get_user_input_string("请输入联系电话: ", user.phone, MAX_PHONE_LEN);
        if (validate_phone(user.phone)) break;
        printf("电话号码格式不正确，请输入11位数字。\n");
    }

    // 获取预约信息
    court_id = get_user_input_int("请输入场地编号 (1-8): ", 1, MAX_COURTS);
    year = get_user_input_int("请输入年份 (如2025): ", 2024, 2030);
    month = get_user_input_int("请输入月份 (1-12): ", 1, 12);
    day = get_user_input_int("请输入日期 (1-31): ", 1, 31);
    hour = get_user_input_int("请输入小时 (0-23): ", 0, 23);

    // 尝试添加预约
    int result = add_reservation(head, court_id, year, month, day, hour, user);

    switch (result) {
        case 0:
            printf("预约成功！\n");
            printf("预约信息：场地%d - %d年%d月%d日 %02d:00 - %s\n",
                   court_id, year, month, day, hour, user.name);
            // 保存到文件
            if (save_reservations_to_file(*head) == 0) {
                printf("预约记录已保存到文件。\n");
            }
            break;
        case -1:
            printf("预约失败：时间无效或不在开放时间内。\n");
            break;
        case -2:
            printf("预约失败：场地编号无效。\n");
            break;
        case -3:
            printf("预约失败：该时间段已被预约。\n");
            break;
        case -4:
            printf("预约失败：系统内存不足。\n");
            break;
        default:
            printf("预约失败：未知错误。\n");
            break;
    }
}

// 处理查看状态
void handle_view_status(Reservation* head) {
    printf("\n=== 三天内场地状态 ===\n");
    display_three_days_status(head);
}

// 处理查询功能
void handle_query(Reservation* head) {
    printf("\n=== 信息查询 ===\n");
    printf("1. 按场地查询\n");
    printf("2. 按手机号查询\n");

    int choice = get_user_input_int("请选择查询方式 (1-2): ", 1, 2);

    if (choice == 1) {
        int court_id = get_user_input_int("请输入场地编号 (1-8): ", 1, MAX_COURTS);
        query_by_court(head, court_id);
    } else {
        char phone[MAX_PHONE_LEN];
        while (1) {
            get_user_input_string("请输入手机号: ", phone, MAX_PHONE_LEN);
            if (validate_phone(phone)) break;
            printf("手机号格式不正确，请重新输入。\n");
        }
        query_by_phone(head, phone);
    }
}

// 处理取消预约
void handle_cancel(Reservation** head) {
    printf("\n=== 取消预约 ===\n");

    int court_id = get_user_input_int("请输入场地编号 (1-8): ", 1, MAX_COURTS);

    char phone[MAX_PHONE_LEN];
    while (1) {
        get_user_input_string("请输入预约时的手机号: ", phone, MAX_PHONE_LEN);
        if (validate_phone(phone)) break;
        printf("手机号格式不正确，请重新输入。\n");
    }

    int result = cancel_reservation(head, court_id, phone);

    switch (result) {
        case 0:
            printf("预约取消成功！\n");
            // 保存到文件
            if (save_reservations_to_file(*head) == 0) {
                printf("预约记录已更新到文件。\n");
            }
            break;
        case -1:
            printf("取消失败：参数无效。\n");
            break;
        case -2:
            printf("取消失败：未找到匹配的预约记录。\n");
            break;
        default:
            printf("取消失败：未知错误。\n");
            break;
    }
}

// 主程序
int main() {
    printf("欢迎使用上海理工大学1100校区羽毛球场预约系统！\n");
    printf("正在加载预约数据...\n");

    // 从文件加载预约记录
    load_reservations_from_file(&reservation_head);
    printf("系统已启动，数据将自动保存在 %s 文件中。\n", FILENAME);

    int choice;
    do {
        display_menu();
        choice = get_user_input_int("", 0, 4);

        switch (choice) {
            case 1:
                handle_add_reservation(&reservation_head);
                break;
            case 2:
                handle_view_status(reservation_head);
                break;
            case 3:
                handle_query(reservation_head);
                break;
            case 4:
                handle_cancel(&reservation_head);
                break;
            case 0:
                printf("正在退出系统...\n");
                break;
            default:
                printf("无效选择，请重新输入。\n");
                break;
        }
    } while (choice != 0);

    // 清理内存
    free_reservation_list(reservation_head);
    printf("系统已退出，感谢使用！\n");

    return 0;
}
