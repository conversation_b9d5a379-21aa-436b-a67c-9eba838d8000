#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>

// Constants
#define MAX_COURTS 8
#define FILENAME "Reserve.txt"

// User information structure
typedef struct {
    char id[10];        // Student/Staff ID
    char name[50];      // Name
    char phone[15];     // Phone number
} User;

// Reservation record structure
typedef struct Node {
    int court;          // Court number
    int year;           // Year
    int month;          // Month
    int day;            // Day
    int hour;           // Hour
    User user;          // User information
    struct Node* next;  // Next node
} Node;

// Global variables
Node* head = NULL;      // Head pointer of linked list

// Function declarations
int check_time(int year, int month, int day, int hour);
int get_weekday(int year, int month, int day);
int add_node(int court, int year, int month, int day, int hour, User user);
void save_file();
void load_file();
void add_booking();
void show_status();
void search_info();
void cancel_booking();

// Main program
int main() {
    // Program startup message
    printf("========================================\n");
    printf("USST 1100 Campus Badminton Court Reservation System\n");
    printf("========================================\n");
    printf("Loading data...\n");
    
    // Load reservation data from file
    load_file();
    printf("Data loaded successfully!\n");
    
    int choice;
    // Main menu loop
    do {
        // Display main menu
        printf("\n========================================\n");
        printf("  USST 1100 Campus Badminton Court System  \n");
        printf("========================================\n");
        printf("1. Add Reservation - Book a court\n");
        printf("2. View Status - Check 3-day court status\n");
        printf("3. Search Info - Query by court or phone\n");
        printf("4. Cancel Reservation - Remove booking\n");
        printf("0. Exit System\n");
        printf("========================================\n");
        printf("Please select function (0-4): ");
        
        // Get user choice
        scanf("%d", &choice);
        
        // Execute corresponding function based on user choice
        switch (choice) {
            case 1:
                add_booking();      // Book court
                break;
            case 2:
                show_status();      // View court status
                break;
            case 3:
                search_info();      // Search reservation info
                break;
            case 4:
                cancel_booking();   // Cancel reservation
                break;
            case 0:
                printf("\nExiting system...\n");
                printf("Thank you for using the Badminton Court Reservation System!\n");
                printf("Goodbye!\n");
                break;
            default:
                printf("Invalid choice! Please enter a number between 0-4.\n");
                break;
        }
    } while (choice != 0);  // Exit loop when user chooses 0
    
    return 0;  // Program ends normally
}

// Calculate day of week (1=Monday, 2=Tuesday, ..., 7=Sunday)
int get_weekday(int year, int month, int day) {
    if (month < 3) {
        month += 12;
        year--;
    }
    int k = year % 100;
    int j = year / 100;
    int h = (day + (13 * (month + 1)) / 5 + k + k / 4 + j / 4 - 2 * j) % 7;
    return (h + 5) % 7 + 1;
}

// Check if time is valid
int check_time(int year, int month, int day, int hour) {
    // Get current time
    time_t now = time(NULL);
    struct tm* t = localtime(&now);
    
    // Check basic range
    if (year < 2024 || year > 2030) return 0;
    if (month < 1 || month > 12) return 0;
    if (day < 1 || day > 31) return 0;
    if (hour < 0 || hour > 23) return 0;
    
    // Check if it's future time
    if (year < t->tm_year + 1900) return 0;
    if (year == t->tm_year + 1900 && month < t->tm_mon + 1) return 0;
    if (year == t->tm_year + 1900 && month == t->tm_mon + 1 && day < t->tm_mday) return 0;
    if (year == t->tm_year + 1900 && month == t->tm_mon + 1 && day == t->tm_mday && hour < t->tm_hour) return 0;
    
    // Calculate day of week
    int weekday = get_weekday(year, month, day);
    
    // Check opening hours
    if (weekday >= 1 && weekday <= 5) {  // Weekdays: Monday to Friday
        if (hour < 15 || hour >= 21) return 0;  // Open 15-21
        // Monday, Wednesday, Friday 17-20 reserved for staff badminton association
        if ((weekday == 1 || weekday == 3 || weekday == 5) && hour >= 17 && hour < 20) return 0;
    } else {  // Weekend: Saturday, Sunday
        if (hour < 9 || hour >= 21) return 0;  // Open 9-21
    }
    
    return 1;  // Time is valid
}

// Add node to linked list
int add_node(int court, int year, int month, int day, int hour, User user) {
    // Check if court number is valid
    if (court < 1 || court > MAX_COURTS) {
        return -3;  // Invalid court number
    }
    
    // Check time conflict
    Node* current = head;
    while (current != NULL) {
        if (current->court == court && current->year == year && 
            current->month == month && current->day == day && current->hour == hour) {
            return -1;  // Time conflict
        }
        current = current->next;
    }
    
    // Create new node
    Node* new_node = (Node*)malloc(sizeof(Node));
    if (new_node == NULL) {
        return -2;  // Memory allocation failed
    }
    
    // Fill node data
    new_node->court = court;
    new_node->year = year;
    new_node->month = month;
    new_node->day = day;
    new_node->hour = hour;
    new_node->user = user;  // Copy user information
    
    // Insert at head of linked list (simplified version)
    new_node->next = head;
    head = new_node;
    
    return 0;  // Add successful
}

// Save reservation records to file
void save_file() {
    // Open file for writing
    FILE* file = fopen(FILENAME, "w");
    if (file == NULL) {
        printf("Cannot save file!\n");
        return;
    }
    
    // Traverse linked list and write each node to file
    Node* current = head;
    while (current != NULL) {
        // Write in CSV format: court,year,month,day,hour,id,name,phone
        fprintf(file, "%d,%d,%d,%d,%d,%s,%s,%s\n",
                current->court, current->year, current->month, current->day, current->hour,
                current->user.id, current->user.name, current->user.phone);
        current = current->next;
    }
    
    // Close file
    fclose(file);
}

// Load reservation records from file
void load_file() {
    // Open file for reading
    FILE* file = fopen(FILENAME, "r");
    if (file == NULL) {
        // File doesn't exist is normal for first run
        return;
    }
    
    char line[200];
    // Read file content line by line
    while (fgets(line, sizeof(line), file)) {
        int court, year, month, day, hour;
        User user;
        
        // Parse CSV format data
        int result = sscanf(line, "%d,%d,%d,%d,%d,%s,%s,%s",
                           &court, &year, &month, &day, &hour,
                           user.id, user.name, user.phone);
        
        // If parsing successful, add to linked list
        if (result == 8) {
            add_node(court, year, month, day, hour, user);
        }
    }
    
    // Close file
    fclose(file);
}

// Court booking function
void add_booking() {
    printf("\n=== Book a Court ===\n");
    
    User user;
    int court, year, month, day, hour;
    
    // Input user information
    printf("Enter Student/Staff ID: ");
    scanf("%s", user.id);
    
    printf("Enter Name: ");
    scanf("%s", user.name);
    
    printf("Enter Phone: ");
    scanf("%s", user.phone);
    
    // Input reservation information
    printf("Enter Court Number (1-8): ");
    scanf("%d", &court);
    
    printf("Enter Year (e.g. 2025): ");
    scanf("%d", &year);
    
    printf("Enter Month (1-12): ");
    scanf("%d", &month);
    
    printf("Enter Day (1-31): ");
    scanf("%d", &day);
    
    printf("Enter Hour (0-23): ");
    scanf("%d", &hour);
    
    // Check if time is valid
    if (!check_time(year, month, day, hour)) {
        printf("Booking failed: Invalid time or not within opening hours!\n");
        return;
    }
    
    // Try to add reservation
    int result = add_node(court, year, month, day, hour, user);
    if (result == 0) {
        printf("Booking successful!\n");
        printf("Booking info: Court %d - %d/%d/%d %02d:00 - %s\n", 
               court, year, month, day, hour, user.name);
        save_file();  // Save to file
    } else if (result == -1) {
        printf("Booking failed: Time slot already booked!\n");
    } else if (result == -2) {
        printf("Booking failed: System memory insufficient!\n");
    } else if (result == -3) {
        printf("Booking failed: Invalid court number!\n");
    } else {
        printf("Booking failed: Unknown error!\n");
    }
}

// Display 3-day court status
void show_status() {
    printf("\n=== 3-Day Court Status ===\n");

    // Get current time
    time_t now = time(NULL);
    struct tm* t = localtime(&now);

    // Weekday names array
    char* weekday_names[] = {"Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"};

    // Display status for 3 days
    for (int day_offset = 0; day_offset < 3; day_offset++) {
        int check_year = t->tm_year + 1900;
        int check_month = t->tm_mon + 1;
        int check_day = t->tm_mday + day_offset;

        // Simple date handling (not considering month crossing)
        if (check_day > 31) {
            check_day -= 31;
            check_month++;
            if (check_month > 12) {
                check_month = 1;
                check_year++;
            }
        }

        // Calculate day of week
        int weekday = get_weekday(check_year, check_month, check_day);

        // Display date title
        printf("\n%d/%d/%d (%s):\n",
               check_year, check_month, check_day, weekday_names[weekday]);

        // Display status for each court
        for (int court = 1; court <= MAX_COURTS; court++) {
            printf("Court %d: ", court);

            // Find reservation records for this court on this date
            Node* current = head;
            int found = 0;
            while (current != NULL) {
                if (current->court == court && current->year == check_year &&
                    current->month == check_month && current->day == check_day) {
                    printf("%02d:00[Booked] ", current->hour);
                    found = 1;
                }
                current = current->next;
            }

            // If no reservation records, show all available
            if (!found) {
                printf("[All Available]");
            }
            printf("\n");
        }
    }
    printf("\n");
}

// Search reservation information
void search_info() {
    printf("\n=== Search Information ===\n");
    printf("1. Search by Court\n");
    printf("2. Search by Phone\n");

    int choice;
    printf("Please select search method (1-2): ");
    scanf("%d", &choice);

    // Search by court
    if (choice == 1) {
        int court;
        printf("Enter Court Number (1-8): ");
        scanf("%d", &court);

        // Check if court number is valid
        if (court < 1 || court > MAX_COURTS) {
            printf("Invalid court number!\n");
            return;
        }

        printf("\nReservation records for Court %d:\n", court);
        printf("----------------------------------------\n");

        // Traverse linked list to find reservation records for this court
        Node* current = head;
        int found = 0;
        while (current != NULL) {
            if (current->court == court) {
                printf("%d/%d/%d %02d:00 - %s(%s) - %s\n",
                       current->year, current->month, current->day, current->hour,
                       current->user.name, current->user.id, current->user.phone);
                found = 1;
            }
            current = current->next;
        }

        if (!found) {
            printf("No reservation records for this court\n");
        }
        printf("----------------------------------------\n");
    }
    // Search by phone
    else if (choice == 2) {
        char phone[15];
        printf("Enter Phone Number: ");
        scanf("%s", phone);

        printf("\nReservation records for phone %s:\n", phone);
        printf("----------------------------------------\n");

        // Traverse linked list to find reservation records for this phone
        Node* current = head;
        int found = 0;
        while (current != NULL) {
            if (strcmp(current->user.phone, phone) == 0) {
                printf("Court %d - %d/%d/%d %02d:00 - %s(%s)\n",
                       current->court, current->year, current->month, current->day, current->hour,
                       current->user.name, current->user.id);
                found = 1;
            }
            current = current->next;
        }

        if (!found) {
            printf("No reservation records for this phone number\n");
        }
        printf("----------------------------------------\n");
    }
    // Invalid choice
    else {
        printf("Invalid choice!\n");
    }
}

// Cancel reservation function
void cancel_booking() {
    printf("\n=== Cancel Reservation ===\n");

    int court;
    char phone[15];

    // Input reservation information to cancel
    printf("Enter Court Number (1-8): ");
    scanf("%d", &court);

    // Check if court number is valid
    if (court < 1 || court > MAX_COURTS) {
        printf("Invalid court number!\n");
        return;
    }

    printf("Enter Phone Number used for reservation: ");
    scanf("%s", phone);

    // Traverse linked list to find reservation record to delete
    Node* current = head;
    Node* prev = NULL;

    while (current != NULL) {
        // Find matching reservation record (both court number and phone match)
        if (current->court == court && strcmp(current->user.phone, phone) == 0) {
            // Display reservation information to be cancelled
            printf("\nFound reservation record:\n");
            printf("Court %d - %d/%d/%d %02d:00 - %s(%s)\n",
                   current->court, current->year, current->month, current->day, current->hour,
                   current->user.name, current->user.id);

            // Delete node
            if (prev == NULL) {
                // Delete head node
                head = current->next;
            } else {
                // Delete middle or tail node
                prev->next = current->next;
            }

            // Free memory
            free(current);

            printf("Reservation cancelled successfully!\n");

            // Save updated data to file
            save_file();
            return;
        }

        // Move to next node
        prev = current;
        current = current->next;
    }

    // No matching reservation record found
    printf("No matching reservation record found!\n");
    printf("Please check if court number and phone number are correct.\n");
}
