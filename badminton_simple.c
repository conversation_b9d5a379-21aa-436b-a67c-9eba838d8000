#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>

// ????????
#define MAX_COURTS 8
#define FILENAME "Reserve.txt"

// ??????????
typedef struct {
    char id[10];        // ????/???
    char name[50];      // ????
    char phone[15];     // ?绰
} User;

// ?????????
typedef struct Node {
    int court;          // ??????
    int year;           // ???
    int month;          // ?·?
    int day;            // ????
    int hour;           // С?
    User user;          // ??????
    struct Node* next;  // ????????
} Node;

// ??????
Node* head = NULL;      // ????????

// ????????
int check_time(int year, int month, int day, int hour);
int get_weekday(int year, int month, int day);
int check_id(char* id);        // check student/staff id
int check_phone(char* phone);  // check phone number
int add_node(int court, int year, int month, int day, int hour, User user);
void save_file();
void load_file();
void add_booking();
void show_status();
void search_info();
void cancel_booking();

// ??????
int main() {
    // ???????????
    printf("========================================\n");
    printf("????????????????1100У?????????????\n");
    printf("========================================\n");
    printf("???????????...\n");

    // ???????????????
    load_file();
    printf("???????????\n");

    int choice;
    // ????????
    do {
        // ????????
        printf("\n========================================\n");
        printf("  ??????????1100У???????????  \n");
        printf("========================================\n");
        printf("1. ?????? - ??????\n");
        printf("2. ?????? - ?????????????\n");
        printf("3. ?????? - ??????????????\n");
        printf("4. ?????? - ?????\n");
        printf("0. ?????\n");
        printf("========================================\n");
        printf("???????(0-4): ");

        // ?????????
        scanf("%d", &choice);

        // ????????????????????
        switch (choice) {
            case 1:
                add_booking();      // ??????
                break;
            case 2:
                show_status();      // ????????
                break;
            case 3:
                search_info();      // ????????
                break;
            case 4:
                cancel_booking();   // ?????
                break;
            case 0:
                printf("\n?????????...\n");
                printf("??л??????????????\n");
                printf("?????\n");
                break;
            default:
                printf("??Ч?????????0-4?????????\n");
                break;
        }
    } while (choice != 0);  // ??????0???????

    return 0;  // ????????????
}

// ????????? (1=???, 2=???, ..., 7=????)
int get_weekday(int year, int month, int day) {
    if (month < 3) {
        month += 12;
        year--;
    }
    int k = year % 100;
    int j = year / 100;
    int h = (day + (13 * (month + 1)) / 5 + k + k / 4 + j / 4 - 2 * j) % 7;
    return (h + 5) % 7 + 1;
}

// 检查学号/工号有效性
int check_id(char* id) {
    int len = strlen(id);
    if (len < 6 || len > 12) return 0;  // 长度检查

    // 检查是否全为数字
    for (int i = 0; i < len; i++) {
        if (id[i] < '0' || id[i] > '9') return 0;
    }
    return 1;  // 有效
}

// 检查电话号码有效性
int check_phone(char* phone) {
    int len = strlen(phone);
    if (len != 11) return 0;  // 手机号必须11位

    // 检查是否全为数字
    for (int i = 0; i < len; i++) {
        if (phone[i] < '0' || phone[i] > '9') return 0;
    }

    // 检查是否以1开头
    if (phone[0] != '1') return 0;

    return 1;  // 有效
}

// ???????????Ч
int check_time(int year, int month, int day, int hour) {
    // ?????????
    time_t now = time(NULL);
    struct tm* t = localtime(&now);

    // ????????Χ
    if (year < 2024 || year > 2030) return 0;
    if (month < 1 || month > 12) return 0;
    if (day < 1 || day > 31) return 0;
    if (hour < 0 || hour > 23) return 0;

    // ???????δ?????
    if (year < t->tm_year + 1900) return 0;
    if (year == t->tm_year + 1900 && month < t->tm_mon + 1) return 0;
    if (year == t->tm_year + 1900 && month == t->tm_mon + 1 && day < t->tm_mday) return 0;
    if (year == t->tm_year + 1900 && month == t->tm_mon + 1 && day == t->tm_mday && hour < t->tm_hour) return 0;

    // ?????????
    int weekday = get_weekday(year, month, day);

    // ????????
    if (weekday >= 1 && weekday <= 5) {  // ????????????????
        if (hour < 15 || hour >= 21) return 0;  // 15-21????
        // ??????????? 17-20????????Э?????
        if ((weekday == 1 || weekday == 3 || weekday == 5) && hour >= 17 && hour < 20) return 0;
    } else {  // ???????????????
        if (hour < 9 || hour >= 21) return 0;  // 9-21????
    }

    return 1;  // ?????Ч
}

// ??????????
int add_node(int court, int year, int month, int day, int hour, User user) {
    // ??鳡?????????Ч
    if (court < 1 || court > MAX_COURTS) {
        return -3;  // ????????Ч
    }

    // ????????
    Node* current = head;
    while (current != NULL) {
        if (current->court == court && current->year == year &&
            current->month == month && current->day == day && current->hour == hour) {
            return -1;  // ?????
        }
        current = current->next;
    }

    // ????????
    Node* new_node = (Node*)malloc(sizeof(Node));
    if (new_node == NULL) {
        return -2;  // ?????????
    }

    // ?????????
    new_node->court = court;
    new_node->year = year;
    new_node->month = month;
    new_node->day = day;
    new_node->hour = hour;
    new_node->user = user;  // ??????????

    // ???????????????汾??
    new_node->next = head;
    head = new_node;

    return 0;  // ??????
}


// ??????????????
void save_file() {
    // ?????????д??
    FILE* file = fopen(FILENAME, "w");
    if (file == NULL) {
        printf("????????????\n");
        return;
    }

    // ??????????????????д?????
    Node* current = head;
    while (current != NULL) {
        // ??CSV???д??????,??,??,??,?,????,????,?绰
        fprintf(file, "%d,%d,%d,%d,%d,%s,%s,%s\n",
                current->court, current->year, current->month, current->day, current->hour,
                current->user.id, current->user.name, current->user.phone);
        current = current->next;
    }

    // ??????
    fclose(file);
}

// ??????????????
void load_file() {
    // ????????ж??
    FILE* file = fopen(FILENAME, "r");
    if (file == NULL) {
        // ??????????????????????????????
        return;
    }

    char line[200];
    // ???ж?????????
    while (fgets(line, sizeof(line), file)) {
        int court, year, month, day, hour;
        User user;

        // ????CSV?????????
        int result = sscanf(line, "%d,%d,%d,%d,%d,%s,%s,%s",
                           &court, &year, &month, &day, &hour,
                           user.id, user.name, user.phone);

        // ?????????????????????
        if (result == 8) {
            add_node(court, year, month, day, hour, user);
        }
    }

    // ??????
    fclose(file);
}

// ?????????
void add_booking() {
    printf("\n=== ?????? ===\n");

    User user;
    int court, year, month, day, hour;

    // ??????????
    printf("????????/???: ");
    scanf("%s", user.id);

    printf("??????????: ");
    scanf("%s", user.name);

    printf("??????绰: ");
    scanf("%s", user.phone);

    // 验证学号/工号
    if (!check_id(user.id)) {
        printf("预约失败：学号/工号格式不正确！(应为6-12位数字)\n");
        return;
    }

    // 验证电话号码
    if (!check_phone(user.phone)) {
        printf("预约失败：电话号码格式不正确！(应为11位手机号)\n");
        return;
    }

    // ?????????
    printf("??????????(1-8): ");
    scanf("%d", &court);

    printf("?????????(??2025): ");
    scanf("%d", &year);

    printf("???????·?(1-12): ");
    scanf("%d", &month);

    printf("??????????(1-31): ");
    scanf("%d", &day);

    printf("??????С?(0-23): ");
    scanf("%d", &hour);

    // ???????????Ч
    if (!check_time(year, month, day, hour)) {
        printf("???????????Ч?????????????\n");
        return;
    }

    // ??????????
    int result = add_node(court, year, month, day, hour, user);
    if (result == 0) {
        printf("???????\n");
        printf("???????????%d - %d??%d??%d?? %02d:00 - %s\n",
               court, year, month, day, hour, user.name);
        save_file();  // ???浽???
    } else if (result == -1) {
        printf("???????????????????\n");
    } else if (result == -2) {
        printf("??????????治??\n");
    } else if (result == -3) {
        printf("??????????????Ч??\n");
    } else {
        printf("??????δ?????\n");
    }
}

// ??????????????
void show_status() {
    printf("\n=== ??????????? ===\n");

    // ?????????
    time_t now = time(NULL);
    struct tm* t = localtime(&now);

    // ????????????
    char* weekday_names[] = {"????", "???", "???", "????", "????", "????", "????"};

    // ??????????
    for (int day_offset = 0; day_offset < 3; day_offset++) {
        int check_year = t->tm_year + 1900;
        int check_month = t->tm_mon + 1;
        int check_day = t->tm_mday + day_offset;

        // ??????????????????????
        if (check_day > 31) {
            check_day -= 31;
            check_month++;
            if (check_month > 12) {
                check_month = 1;
                check_year++;
            }
        }

        // ?????????
        int weekday = get_weekday(check_year, check_month, check_day);

        // ??????????
        printf("\n%d??%d??%d?? (%s):\n",
               check_year, check_month, check_day, weekday_names[weekday]);

        // ?????????????
        for (int court = 1; court <= MAX_COURTS; court++) {
            printf("????%d: ", court);

            // ????ó????????????????
            Node* current = head;
            int found = 0;
            while (current != NULL) {
                if (current->court == court && current->year == check_year &&
                    current->month == check_month && current->day == check_day) {
                    printf("%02d:00[???] ", current->hour);
                    found = 1;
                }
                current = current->next;
            }

            // ??????????????????????
            if (!found) {
                printf("[??????]");
            }
            printf("\n");
        }
    }
    printf("\n");
}

// ????????
void search_info() {
    printf("\n=== ?????? ===\n");
    printf("1. ????????\n");
    printf("2. ?????????\n");

    int choice;
    printf("??????????(1-2): ");
    scanf("%d", &choice);

    // ????????
    if (choice == 1) {
        int court;
        printf("??????????(1-8): ");
        scanf("%d", &court);

        // ??鳡?????????Ч
        if (court < 1 || court > MAX_COURTS) {
            printf("????????Ч??\n");
            return;
        }

        printf("\n????%d???????:\n", court);
        printf("----------------------------------------\n");

        // ????????????ó?????????
        Node* current = head;
        int found = 0;
        while (current != NULL) {
            if (current->court == court) {
                printf("%d??%d??%d?? %02d:00 - %s(%s) - %s\n",
                       current->year, current->month, current->day, current->hour,
                       current->user.name, current->user.id, current->user.phone);
                found = 1;
            }
            current = current->next;
        }

        if (!found) {
            printf("?ó????????????\n");
        }
        printf("----------------------------------------\n");
    }
    // ?????????
    else if (choice == 2) {
        char phone[15];
        printf("???????????: ");
        scanf("%s", phone);

        printf("\n?????%s???????:\n", phone);
        printf("----------------------------------------\n");

        // ????????????????????????
        Node* current = head;
        int found = 0;
        while (current != NULL) {
            if (strcmp(current->user.phone, phone) == 0) {
                printf("????%d - %d??%d??%d?? %02d:00 - %s(%s)\n",
                       current->court, current->year, current->month, current->day, current->hour,
                       current->user.name, current->user.id);
                found = 1;
            }
            current = current->next;
        }

        if (!found) {
            printf("????????????????\n");
        }
        printf("----------------------------------------\n");
    }
    // ??Ч???
    else {
        printf("??Ч?????\n");
    }
}

// ?????????
void cancel_booking() {
    printf("\n=== ????? ===\n");

    int court;
    char phone[15];

    // ???????????????
    printf("??????????(1-8): ");
    scanf("%d", &court);

    // ??鳡?????????Ч
    if (court < 1 || court > MAX_COURTS) {
        printf("????????Ч??\n");
        return;
    }

    printf("????????????????: ");
    scanf("%s", phone);

    // ???????????????????????
    Node* current = head;
    Node* prev = NULL;

    while (current != NULL) {
        // ??????????????????????????????
        if (current->court == court && strcmp(current->user.phone, phone) == 0) {
            // ??????????????
            printf("\n??????????\n");
            printf("????%d - %d??%d??%d?? %02d:00 - %s(%s)\n",
                   current->court, current->year, current->month, current->day, current->hour,
                   current->user.name, current->user.id);

            // ??????
            if (prev == NULL) {
                // ???????
                head = current->next;
            } else {
                // ????м??β???
                prev->next = current->next;
            }

            // ??????
            free(current);

            printf("??????????\n");

            // ?????????????????
            save_file();
            return;
        }

        // ?????????????
        prev = current;
        current = current->next;
    }

    // ???????????????
    printf("δ??????????????\n");
    printf("???鳡??????????????????\n");
}
