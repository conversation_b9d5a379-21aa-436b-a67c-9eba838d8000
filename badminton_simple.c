#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>

// ��������
#define MAX_COURTS 8
#define FILENAME "Reserve.txt"

// �û���Ϣ�ṹ��
typedef struct {
    char id[10];        // ����/ѧ��
    char name[50];      // ����
    char phone[15];     // �绰
} User;

// ԤԼ��¼�ṹ��
typedef struct Node {
    int court;          // ���ر��
    int year;           // ���
    int month;          // �·�
    int day;            // ����
    int hour;           // Сʱ
    User user;          // �û���Ϣ
    struct Node* next;  // ��һ���ڵ�
} Node;

// ȫ�ֱ���
Node* head = NULL;      // ����ͷָ��

// ��������
int check_time(int year, int month, int day, int hour);
int get_weekday(int year, int month, int day);
int check_id(char* id);        // check student/staff id
int check_phone(char* phone);  // check phone number
int add_node(int court, int year, int month, int day, int hour, User user);
void save_file();
void load_file();
void add_booking();
void show_status();
void search_info();
void cancel_booking();

// ������
int main() {
    // ����������ʾ
    printf("========================================\n");
    printf("��ӭʹ���Ϻ�������ѧ1100У����ë��ԤԼϵͳ��\n");
    printf("========================================\n");
    printf("���ڼ�������...\n");

    // ���ļ�����ԤԼ����
    load_file();
    printf("���ݼ�����ɣ�\n");

    int choice;
    // ���˵�ѭ��
    do {
        // ��ʾ���˵�
        printf("\n========================================\n");
        printf("  �Ϻ�������ѧ1100У����ë��ԤԼϵͳ  \n");
        printf("========================================\n");
        printf("1. ��Ϣ¼�� - ԤԼ����\n");
        printf("2. ��Ϣ��� - �鿴�����ڳ���״̬\n");
        printf("3. ��Ϣ��ѯ - �����ػ��ֻ��Ų�ѯ\n");
        printf("4. ��Ϣ�޸� - ȡ��ԤԼ\n");
        printf("0. �˳�ϵͳ\n");
        printf("========================================\n");
        printf("��ѡ����(0-4): ");

        // ��ȡ�û�ѡ��
        scanf("%d", &choice);

        // �����û�ѡ��ִ����Ӧ����
        switch (choice) {
            case 1:
                add_booking();      // ԤԼ����
                break;
            case 2:
                show_status();      // �鿴����״̬
                break;
            case 3:
                search_info();      // ��ѯԤԼ��Ϣ
                break;
            case 4:
                cancel_booking();   // ȡ��ԤԼ
                break;
            case 0:
                printf("\n�����˳�ϵͳ...\n");
                printf("��лʹ����ë��ԤԼϵͳ��\n");
                printf("�ټ���\n");
                break;
            default:
                printf("��Чѡ��������0-4֮������֡�\n");
                break;
        }
    } while (choice != 0);  // �û�ѡ��0ʱ�˳�ѭ��

    return 0;  // ������������
}

// �������ڼ� (1=��һ, 2=�ܶ�, ..., 7=����)
int get_weekday(int year, int month, int day) {
    if (month < 3) {
        month += 12;
        year--;
    }
    int k = year % 100;
    int j = year / 100;
    int h = (day + (13 * (month + 1)) / 5 + k + k / 4 + j / 4 - 2 * j) % 7;
    return (h + 5) % 7 + 1;
}

// 检查学号/工号有效性
int check_id(char* id) {
    int len = strlen(id);
    if (len < 6 || len > 12) return 0;  // 长度检查

    // 检查是否全为数字
    for (int i = 0; i < len; i++) {
        if (id[i] < '0' || id[i] > '9') return 0;
    }
    return 1;  // 有效
}

// 检查电话号码有效性
int check_phone(char* phone) {
    int len = strlen(phone);
    if (len != 11) return 0;  // 手机号必须11位

    // 检查是否全为数字
    for (int i = 0; i < len; i++) {
        if (phone[i] < '0' || phone[i] > '9') return 0;
    }

    // 检查是否以1开头
    if (phone[0] != '1') return 0;

    return 1;  // 有效
}

// ���ʱ���Ƿ���Ч
int check_time(int year, int month, int day, int hour) {
    // ��ȡ��ǰʱ��
    time_t now = time(NULL);
    struct tm* t = localtime(&now);

    // ��������Χ
    if (year < 2024 || year > 2030) return 0;
    if (month < 1 || month > 12) return 0;
    if (day < 1 || day > 31) return 0;
    if (hour < 0 || hour > 23) return 0;

    // ����Ƿ�Ϊδ��ʱ��
    if (year < t->tm_year + 1900) return 0;
    if (year == t->tm_year + 1900 && month < t->tm_mon + 1) return 0;
    if (year == t->tm_year + 1900 && month == t->tm_mon + 1 && day < t->tm_mday) return 0;
    if (year == t->tm_year + 1900 && month == t->tm_mon + 1 && day == t->tm_mday && hour < t->tm_hour) return 0;

    // �������ڼ�
    int weekday = get_weekday(year, month, day);

    // ��鿪��ʱ��
    if (weekday >= 1 && weekday <= 5) {  // �����գ���һ������
        if (hour < 15 || hour >= 21) return 0;  // 15-21�㿪��
        // ��һ�������� 17-20��Ϊ�̹���Э�ʱ��
        if ((weekday == 1 || weekday == 3 || weekday == 5) && hour >= 17 && hour < 20) return 0;
    } else {  // ��ĩ������������
        if (hour < 9 || hour >= 21) return 0;  // 9-21�㿪��
    }

    return 1;  // ʱ����Ч
}

// ���ӽڵ㵽����
int add_node(int court, int year, int month, int day, int hour, User user) {
    // ��鳡�ر���Ƿ���Ч
    if (court < 1 || court > MAX_COURTS) {
        return -3;  // ���ر����Ч
    }

    // ���ʱ���ͻ
    Node* current = head;
    while (current != NULL) {
        if (current->court == court && current->year == year &&
            current->month == month && current->day == day && current->hour == hour) {
            return -1;  // ʱ���ͻ
        }
        current = current->next;
    }

    // �����½ڵ�
    Node* new_node = (Node*)malloc(sizeof(Node));
    if (new_node == NULL) {
        return -2;  // �ڴ����ʧ��
    }

    // ���ڵ�����
    new_node->court = court;
    new_node->year = year;
    new_node->month = month;
    new_node->day = day;
    new_node->hour = hour;
    new_node->user = user;  // �����û���Ϣ

    // ��������ͷ�����򻯰汾��
    new_node->next = head;
    head = new_node;

    return 0;  // ���ӳɹ�
}


// ����ԤԼ��¼���ļ�
void save_file() {
    // ���ļ�����д��
    FILE* file = fopen(FILENAME, "w");
    if (file == NULL) {
        printf("�޷������ļ���\n");
        return;
    }

    // ������������ÿ���ڵ�д���ļ�
    Node* current = head;
    while (current != NULL) {
        // ��CSV��ʽд�룺����,��,��,��,ʱ,����,����,�绰
        fprintf(file, "%d,%d,%d,%d,%d,%s,%s,%s\n",
                current->court, current->year, current->month, current->day, current->hour,
                current->user.id, current->user.name, current->user.phone);
        current = current->next;
    }

    // �ر��ļ�
    fclose(file);
}

// ���ļ�����ԤԼ��¼
void load_file() {
    // ���ļ����ж�ȡ
    FILE* file = fopen(FILENAME, "r");
    if (file == NULL) {
        // �ļ��������������������һ������ʱ
        return;
    }

    char line[200];
    // ���ж�ȡ�ļ�����
    while (fgets(line, sizeof(line), file)) {
        int court, year, month, day, hour;
        User user;

        // ����CSV��ʽ������
        int result = sscanf(line, "%d,%d,%d,%d,%d,%s,%s,%s",
                           &court, &year, &month, &day, &hour,
                           user.id, user.name, user.phone);

        // ��������ɹ������ӵ�����
        if (result == 8) {
            add_node(court, year, month, day, hour, user);
        }
    }

    // �ر��ļ�
    fclose(file);
}

// ԤԼ���ع���
void add_booking() {
    printf("\n=== ԤԼ���� ===\n");

    User user;
    int court, year, month, day, hour;

    // �����û���Ϣ
    printf("�����빤��/ѧ��: ");
    scanf("%s", user.id);

    printf("����������: ");
    scanf("%s", user.name);

    printf("������绰: ");
    scanf("%s", user.phone);

    // 验证学号/工号
    if (!check_id(user.id)) {
        printf("预约失败：学号/工号格式不正确！(应为6-12位数字)\n");
        return;
    }

    // 验证电话号码
    if (!check_phone(user.phone)) {
        printf("预约失败：电话号码格式不正确！(应为11位手机号)\n");
        return;
    }

    // ����ԤԼ��Ϣ
    printf("�����볡�ر��(1-8): ");
    scanf("%d", &court);

    printf("���������(��2025): ");
    scanf("%d", &year);

    printf("�������·�(1-12): ");
    scanf("%d", &month);

    printf("����������(1-31): ");
    scanf("%d", &day);

    printf("������Сʱ(0-23): ");
    scanf("%d", &hour);

    // ���ʱ���Ƿ���Ч
    if (!check_time(year, month, day, hour)) {
        printf("ԤԼʧ�ܣ�ʱ����Ч���ڿ���ʱ���ڣ�\n");
        return;
    }

    // ��������ԤԼ
    int result = add_node(court, year, month, day, hour, user);
    if (result == 0) {
        printf("ԤԼ�ɹ���\n");
        printf("ԤԼ��Ϣ������%d - %d��%d��%d�� %02d:00 - %s\n",
               court, year, month, day, hour, user.name);
        save_file();  // ���浽�ļ�
    } else if (result == -1) {
        printf("ԤԼʧ�ܣ���ʱ����ѱ�ԤԼ��\n");
    } else if (result == -2) {
        printf("ԤԼʧ�ܣ�ϵͳ�ڴ治�㣡\n");
    } else if (result == -3) {
        printf("ԤԼʧ�ܣ����ر����Ч��\n");
    } else {
        printf("ԤԼʧ�ܣ�δ֪����\n");
    }
}

// ��ʾ�����ڳ���״̬
void show_status() {
    printf("\n=== �����ڳ���״̬ ===\n");

    // ��ȡ��ǰʱ��
    time_t now = time(NULL);
    struct tm* t = localtime(&now);

    // ������������
    char* weekday_names[] = {"����", "��һ", "�ܶ�", "����", "����", "����", "����"};

    // ��ʾ�����״̬
    for (int day_offset = 0; day_offset < 3; day_offset++) {
        int check_year = t->tm_year + 1900;
        int check_month = t->tm_mon + 1;
        int check_day = t->tm_mday + day_offset;

        // �򵥵����ڴ����������ǿ��£�
        if (check_day > 31) {
            check_day -= 31;
            check_month++;
            if (check_month > 12) {
                check_month = 1;
                check_year++;
            }
        }

        // �������ڼ�
        int weekday = get_weekday(check_year, check_month, check_day);

        // ��ʾ���ڱ���
        printf("\n%d��%d��%d�� (%s):\n",
               check_year, check_month, check_day, weekday_names[weekday]);

        // ��ʾÿ�����ص�״̬
        for (int court = 1; court <= MAX_COURTS; court++) {
            printf("����%d: ", court);

            // ���Ҹó����ڸ����ڵ�ԤԼ��¼
            Node* current = head;
            int found = 0;
            while (current != NULL) {
                if (current->court == court && current->year == check_year &&
                    current->month == check_month && current->day == check_day) {
                    printf("%02d:00[��Լ] ", current->hour);
                    found = 1;
                }
                current = current->next;
            }

            // ���û��ԤԼ��¼����ʾȫ����Լ
            if (!found) {
                printf("[ȫ����Լ]");
            }
            printf("\n");
        }
    }
    printf("\n");
}

// ��ѯԤԼ��Ϣ
void search_info() {
    printf("\n=== ��Ϣ��ѯ ===\n");
    printf("1. �����ز�ѯ\n");
    printf("2. ���ֻ��Ų�ѯ\n");

    int choice;
    printf("��ѡ���ѯ��ʽ(1-2): ");
    scanf("%d", &choice);

    // �����ز�ѯ
    if (choice == 1) {
        int court;
        printf("�����볡�ر��(1-8): ");
        scanf("%d", &court);

        // ��鳡�ر���Ƿ���Ч
        if (court < 1 || court > MAX_COURTS) {
            printf("���ر����Ч��\n");
            return;
        }

        printf("\n����%d��ԤԼ��¼:\n", court);
        printf("----------------------------------------\n");

        // �����������Ҹó��ص�ԤԼ��¼
        Node* current = head;
        int found = 0;
        while (current != NULL) {
            if (current->court == court) {
                printf("%d��%d��%d�� %02d:00 - %s(%s) - %s\n",
                       current->year, current->month, current->day, current->hour,
                       current->user.name, current->user.id, current->user.phone);
                found = 1;
            }
            current = current->next;
        }

        if (!found) {
            printf("�ó�������ԤԼ��¼\n");
        }
        printf("----------------------------------------\n");
    }
    // ���ֻ��Ų�ѯ
    else if (choice == 2) {
        char phone[15];
        printf("�������ֻ���: ");
        scanf("%s", phone);

        printf("\n�ֻ���%s��ԤԼ��¼:\n", phone);
        printf("----------------------------------------\n");

        // �����������Ҹ��ֻ��ŵ�ԤԼ��¼
        Node* current = head;
        int found = 0;
        while (current != NULL) {
            if (strcmp(current->user.phone, phone) == 0) {
                printf("����%d - %d��%d��%d�� %02d:00 - %s(%s)\n",
                       current->court, current->year, current->month, current->day, current->hour,
                       current->user.name, current->user.id);
                found = 1;
            }
            current = current->next;
        }

        if (!found) {
            printf("���ֻ�������ԤԼ��¼\n");
        }
        printf("----------------------------------------\n");
    }
    // ��Чѡ��
    else {
        printf("��Ч��ѡ��\n");
    }
}

// ȡ��ԤԼ����
void cancel_booking() {
    printf("\n=== ȡ��ԤԼ ===\n");

    int court;
    char phone[15];

    // ����Ҫȡ����ԤԼ��Ϣ
    printf("�����볡�ر��(1-8): ");
    scanf("%d", &court);

    // ��鳡�ر���Ƿ���Ч
    if (court < 1 || court > MAX_COURTS) {
        printf("���ر����Ч��\n");
        return;
    }

    printf("������ԤԼʱ���ֻ���: ");
    scanf("%s", phone);

    // ������������Ҫɾ����ԤԼ��¼
    Node* current = head;
    Node* prev = NULL;

    while (current != NULL) {
        // �ҵ�ƥ���ԤԼ��¼�����ر�ź��ֻ��Ŷ�ƥ�䣩
        if (current->court == court && strcmp(current->user.phone, phone) == 0) {
            // ��ʾҪȡ����ԤԼ��Ϣ
            printf("\n�ҵ�ԤԼ��¼��\n");
            printf("����%d - %d��%d��%d�� %02d:00 - %s(%s)\n",
                   current->court, current->year, current->month, current->day, current->hour,
                   current->user.name, current->user.id);

            // ɾ���ڵ�
            if (prev == NULL) {
                // ɾ��ͷ�ڵ�
                head = current->next;
            } else {
                // ɾ���м��β�ڵ�
                prev->next = current->next;
            }

            // �ͷ��ڴ�
            free(current);

            printf("ԤԼȡ���ɹ���\n");

            // ������º�����ݵ��ļ�
            save_file();
            return;
        }

        // �ƶ�����һ���ڵ�
        prev = current;
        current = current->next;
    }

    // û���ҵ�ƥ���ԤԼ��¼
    printf("δ�ҵ�ƥ���ԤԼ��¼��\n");
    printf("���鳡�ر�ź��ֻ����Ƿ���ȷ��\n");
}
