#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <windows.h>

// 常量定义
#define MAX_COURTS 8
#define FILENAME "Reserve.txt"

// 用户信息结构体
typedef struct {
    char id[10];        // 工号/学号
    char name[50];      // 姓名
    char phone[15];     // 电话
} User;

// 预约记录结构体
typedef struct Node {
    int court;          // 场地编号
    int year;           // 年份
    int month;          // 月份
    int day;            // 日期
    int hour;           // 小时
    User user;          // 用户信息
    struct Node* next;  // 下一个节点
} Node;

// 全局变量
Node* head = NULL;      // 链表头指针

// 函数声明
int check_time(int year, int month, int day, int hour);
int get_weekday(int year, int month, int day);
int add_node(int court, int year, int month, int day, int hour, User user);
void save_file();
void load_file();
void add_booking();
void show_status();
void search_info();
void cancel_booking();

// 主程序
int main() {
    // 设置控制台编码为GBK，解决中文乱码
    SetConsoleOutputCP(936);
    SetConsoleCP(936);

    // 程序启动提示
    printf("========================================\n");
    printf("欢迎使用上海理工大学1100校区羽毛球场预约系统！\n");
    printf("========================================\n");
    printf("正在加载数据...\n");

    // 从文件加载预约数据
    load_file();
    printf("数据加载完成！\n");

    int choice;
    // 主菜单循环
    do {
        // 显示主菜单
        printf("\n========================================\n");
        printf("  上海理工大学1100校区羽毛球场预约系统  \n");
        printf("========================================\n");
        printf("1. 信息录入 - 预约场地\n");
        printf("2. 信息输出 - 查看三天内场地状态\n");
        printf("3. 信息查询 - 按场地或手机号查询\n");
        printf("4. 信息修改 - 取消预约\n");
        printf("0. 退出系统\n");
        printf("========================================\n");
        printf("请选择功能(0-4): ");

        // 获取用户选择
        scanf("%d", &choice);

        // 根据用户选择执行相应功能
        switch (choice) {
            case 1:
                add_booking();      // 预约场地
                break;
            case 2:
                show_status();      // 查看场地状态
                break;
            case 3:
                search_info();      // 查询预约信息
                break;
            case 4:
                cancel_booking();   // 取消预约
                break;
            case 0:
                printf("\n正在退出系统...\n");
                printf("感谢使用羽毛球场预约系统！\n");
                printf("再见！\n");
                break;
            default:
                printf("无效选择！请输入0-4之间的数字。\n");
                break;
        }
    } while (choice != 0);  // 用户选择0时退出循环

    return 0;  // 程序正常结束
}

// 计算星期几 (1=周一, 2=周二, ..., 7=周日)
int get_weekday(int year, int month, int day) {
    if (month < 3) {
        month += 12;
        year--;
    }
    int k = year % 100;
    int j = year / 100;
    int h = (day + (13 * (month + 1)) / 5 + k + k / 4 + j / 4 - 2 * j) % 7;
    return (h + 5) % 7 + 1;
}

// 检查时间是否有效
int check_time(int year, int month, int day, int hour) {
    // 获取当前时间
    time_t now = time(NULL);
    struct tm* t = localtime(&now);

    // 检查基本范围
    if (year < 2024 || year > 2030) return 0;
    if (month < 1 || month > 12) return 0;
    if (day < 1 || day > 31) return 0;
    if (hour < 0 || hour > 23) return 0;

    // 检查是否为未来时间
    if (year < t->tm_year + 1900) return 0;
    if (year == t->tm_year + 1900 && month < t->tm_mon + 1) return 0;
    if (year == t->tm_year + 1900 && month == t->tm_mon + 1 && day < t->tm_mday) return 0;
    if (year == t->tm_year + 1900 && month == t->tm_mon + 1 && day == t->tm_mday && hour < t->tm_hour) return 0;

    // 计算星期几
    int weekday = get_weekday(year, month, day);

    // 检查开放时间
    if (weekday >= 1 && weekday <= 5) {  // 工作日：周一到周五
        if (hour < 15 || hour >= 21) return 0;  // 15-21点开放
        // 周一、三、五 17-20点为教工羽协活动时间
        if ((weekday == 1 || weekday == 3 || weekday == 5) && hour >= 17 && hour < 20) return 0;
    } else {  // 周末：周六、周日
        if (hour < 9 || hour >= 21) return 0;  // 9-21点开放
    }

    return 1;  // 时间有效
}

// 添加节点到链表
int add_node(int court, int year, int month, int day, int hour, User user) {
    // 检查场地编号是否有效
    if (court < 1 || court > MAX_COURTS) {
        return -3;  // 场地编号无效
    }

    // 检查时间冲突
    Node* current = head;
    while (current != NULL) {
        if (current->court == court && current->year == year &&
            current->month == month && current->day == day && current->hour == hour) {
            return -1;  // 时间冲突
        }
        current = current->next;
    }

    // 创建新节点
    Node* new_node = (Node*)malloc(sizeof(Node));
    if (new_node == NULL) {
        return -2;  // 内存分配失败
    }

    // 填充节点数据
    new_node->court = court;
    new_node->year = year;
    new_node->month = month;
    new_node->day = day;
    new_node->hour = hour;
    new_node->user = user;  // 复制用户信息

    // 插入链表头部（简化版本）
    new_node->next = head;
    head = new_node;

    return 0;  // 添加成功
}


// 保存预约记录到文件
void save_file() {
    // 打开文件进行写入
    FILE* file = fopen(FILENAME, "w");
    if (file == NULL) {
        printf("无法保存文件！\n");
        return;
    }

    // 遍历链表，将每个节点写入文件
    Node* current = head;
    while (current != NULL) {
        // 按CSV格式写入：场地,年,月,日,时,工号,姓名,电话
        fprintf(file, "%d,%d,%d,%d,%d,%s,%s,%s\n",
                current->court, current->year, current->month, current->day, current->hour,
                current->user.id, current->user.name, current->user.phone);
        current = current->next;
    }

    // 关闭文件
    fclose(file);
}

// 从文件加载预约记录
void load_file() {
    // 打开文件进行读取
    FILE* file = fopen(FILENAME, "r");
    if (file == NULL) {
        // 文件不存在是正常情况，第一次运行时
        return;
    }

    char line[200];
    // 逐行读取文件内容
    while (fgets(line, sizeof(line), file)) {
        int court, year, month, day, hour;
        User user;

        // 解析CSV格式的数据
        int result = sscanf(line, "%d,%d,%d,%d,%d,%s,%s,%s",
                           &court, &year, &month, &day, &hour,
                           user.id, user.name, user.phone);

        // 如果解析成功，添加到链表
        if (result == 8) {
            add_node(court, year, month, day, hour, user);
        }
    }

    // 关闭文件
    fclose(file);
}

// 预约场地功能
void add_booking() {
    printf("\n=== 预约场地 ===\n");

    User user;
    int court, year, month, day, hour;

    // 输入用户信息
    printf("请输入工号/学号: ");
    scanf("%s", user.id);

    printf("请输入姓名: ");
    scanf("%s", user.name);

    printf("请输入电话: ");
    scanf("%s", user.phone);

    // 输入预约信息
    printf("请输入场地编号(1-8): ");
    scanf("%d", &court);

    printf("请输入年份(如2025): ");
    scanf("%d", &year);

    printf("请输入月份(1-12): ");
    scanf("%d", &month);

    printf("请输入日期(1-31): ");
    scanf("%d", &day);

    printf("请输入小时(0-23): ");
    scanf("%d", &hour);

    // 检查时间是否有效
    if (!check_time(year, month, day, hour)) {
        printf("预约失败：时间无效或不在开放时间内！\n");
        return;
    }

    // 尝试添加预约
    int result = add_node(court, year, month, day, hour, user);
    if (result == 0) {
        printf("预约成功！\n");
        printf("预约信息：场地%d - %d年%d月%d日 %02d:00 - %s\n",
               court, year, month, day, hour, user.name);
        save_file();  // 保存到文件
    } else if (result == -1) {
        printf("预约失败：该时间段已被预约！\n");
    } else if (result == -2) {
        printf("预约失败：系统内存不足！\n");
    } else if (result == -3) {
        printf("预约失败：场地编号无效！\n");
    } else {
        printf("预约失败：未知错误！\n");
    }
}

// 显示三天内场地状态
void show_status() {
    printf("\n=== 三天内场地状态 ===\n");

    // 获取当前时间
    time_t now = time(NULL);
    struct tm* t = localtime(&now);

    // 星期名称数组
    char* weekday_names[] = {"周日", "周一", "周二", "周三", "周四", "周五", "周六"};

    // 显示三天的状态
    for (int day_offset = 0; day_offset < 3; day_offset++) {
        int check_year = t->tm_year + 1900;
        int check_month = t->tm_mon + 1;
        int check_day = t->tm_mday + day_offset;

        // 简单的日期处理（不考虑跨月）
        if (check_day > 31) {
            check_day -= 31;
            check_month++;
            if (check_month > 12) {
                check_month = 1;
                check_year++;
            }
        }

        // 计算星期几
        int weekday = get_weekday(check_year, check_month, check_day);

        // 显示日期标题
        printf("\n%d年%d月%d日 (%s):\n",
               check_year, check_month, check_day, weekday_names[weekday]);

        // 显示每个场地的状态
        for (int court = 1; court <= MAX_COURTS; court++) {
            printf("场地%d: ", court);

            // 查找该场地在该日期的预约记录
            Node* current = head;
            int found = 0;
            while (current != NULL) {
                if (current->court == court && current->year == check_year &&
                    current->month == check_month && current->day == check_day) {
                    printf("%02d:00[已约] ", current->hour);
                    found = 1;
                }
                current = current->next;
            }

            // 如果没有预约记录，显示全部可约
            if (!found) {
                printf("[全部可约]");
            }
            printf("\n");
        }
    }
    printf("\n");
}

// 查询预约信息
void search_info() {
    printf("\n=== 信息查询 ===\n");
    printf("1. 按场地查询\n");
    printf("2. 按手机号查询\n");

    int choice;
    printf("请选择查询方式(1-2): ");
    scanf("%d", &choice);

    // 按场地查询
    if (choice == 1) {
        int court;
        printf("请输入场地编号(1-8): ");
        scanf("%d", &court);

        // 检查场地编号是否有效
        if (court < 1 || court > MAX_COURTS) {
            printf("场地编号无效！\n");
            return;
        }

        printf("\n场地%d的预约记录:\n", court);
        printf("----------------------------------------\n");

        // 遍历链表查找该场地的预约记录
        Node* current = head;
        int found = 0;
        while (current != NULL) {
            if (current->court == court) {
                printf("%d年%d月%d日 %02d:00 - %s(%s) - %s\n",
                       current->year, current->month, current->day, current->hour,
                       current->user.name, current->user.id, current->user.phone);
                found = 1;
            }
            current = current->next;
        }

        if (!found) {
            printf("该场地暂无预约记录\n");
        }
        printf("----------------------------------------\n");
    }
    // 按手机号查询
    else if (choice == 2) {
        char phone[15];
        printf("请输入手机号: ");
        scanf("%s", phone);

        printf("\n手机号%s的预约记录:\n", phone);
        printf("----------------------------------------\n");

        // 遍历链表查找该手机号的预约记录
        Node* current = head;
        int found = 0;
        while (current != NULL) {
            if (strcmp(current->user.phone, phone) == 0) {
                printf("场地%d - %d年%d月%d日 %02d:00 - %s(%s)\n",
                       current->court, current->year, current->month, current->day, current->hour,
                       current->user.name, current->user.id);
                found = 1;
            }
            current = current->next;
        }

        if (!found) {
            printf("该手机号暂无预约记录\n");
        }
        printf("----------------------------------------\n");
    }
    // 无效选择
    else {
        printf("无效的选择！\n");
    }
}

// 取消预约功能
void cancel_booking() {
    printf("\n=== 取消预约 ===\n");

    int court;
    char phone[15];

    // 输入要取消的预约信息
    printf("请输入场地编号(1-8): ");
    scanf("%d", &court);

    // 检查场地编号是否有效
    if (court < 1 || court > MAX_COURTS) {
        printf("场地编号无效！\n");
        return;
    }

    printf("请输入预约时的手机号: ");
    scanf("%s", phone);

    // 遍历链表查找要删除的预约记录
    Node* current = head;
    Node* prev = NULL;

    while (current != NULL) {
        // 找到匹配的预约记录（场地编号和手机号都匹配）
        if (current->court == court && strcmp(current->user.phone, phone) == 0) {
            // 显示要取消的预约信息
            printf("\n找到预约记录：\n");
            printf("场地%d - %d年%d月%d日 %02d:00 - %s(%s)\n",
                   current->court, current->year, current->month, current->day, current->hour,
                   current->user.name, current->user.id);

            // 删除节点
            if (prev == NULL) {
                // 删除头节点
                head = current->next;
            } else {
                // 删除中间或尾节点
                prev->next = current->next;
            }

            // 释放内存
            free(current);

            printf("预约取消成功！\n");

            // 保存更新后的数据到文件
            save_file();
            return;
        }

        // 移动到下一个节点
        prev = current;
        current = current->next;
    }

    // 没有找到匹配的预约记录
    printf("未找到匹配的预约记录！\n");
    printf("请检查场地编号和手机号是否正确。\n");
}
