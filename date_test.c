#include <stdio.h>

// 计算星期几 (1=周一, 2=周二, ..., 7=周日)
int get_weekday(int year, int month, int day) {
    if (month < 3) { month += 12; year--; }
    int k = year % 100, j = year / 100;
    int h = (day + (13 * (month + 1)) / 5 + k + k / 4 + j / 4 - 2 * j) % 7;
    return (h + 5) % 7 + 1;
}

// 检查场地在指定时间是否开放
int is_court_available_time(int weekday, int hour) {
    printf("检查时间：星期%d，%d点\n", weekday, hour);
    
    if (weekday >= 1 && weekday <= 5) { // 工作日
        printf("工作日规则：15-21点开放\n");
        if (hour < 15 || hour >= 21) {
            printf("不在工作日开放时间内\n");
            return 0;
        }
        if ((weekday == 1 || weekday == 3 || weekday == 5) &&
            hour >= 17 && hour < 20) {
            printf("周一三五17-20点为教工羽协活动时间\n");
            return 0;
        }
        printf("工作日时间可用\n");
        return 1;
    }
    if (weekday == 6 || weekday == 7) { // 周末
        printf("周末规则：9-21点开放\n");
        if (hour >= 9 && hour < 21) {
            printf("周末时间可用\n");
            return 1;
        } else {
            printf("不在周末开放时间内\n");
            return 0;
        }
    }
    printf("无效的星期\n");
    return 0;
}

int main() {
    int year = 2025, month = 7, day = 1, hour = 16;
    
    printf("测试日期：%d年%d月%d日 %d:00\n", year, month, day, hour);
    
    int weekday = get_weekday(year, month, day);
    char* weekday_names[] = {"", "周一", "周二", "周三", "周四", "周五", "周六", "周日"};
    printf("星期几：%d (%s)\n", weekday, weekday_names[weekday]);
    
    int available = is_court_available_time(weekday, hour);
    printf("是否可预约：%s\n", available ? "是" : "否");
    
    return 0;
}
