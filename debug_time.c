#include <stdio.h>
#include <time.h>

// 获取当前系统时间
void get_current_time(int* year, int* month, int* day, int* hour) {
    time_t rawtime;
    struct tm* timeinfo;
    time(&rawtime);
    timeinfo = localtime(&rawtime);
    *year = timeinfo->tm_year + 1900;
    *month = timeinfo->tm_mon + 1;
    *day = timeinfo->tm_mday;
    *hour = timeinfo->tm_hour;
}

// 计算星期几 (1=周一, 2=周二, ..., 7=周日)
int get_weekday(int year, int month, int day) {
    if (month < 3) { month += 12; year--; }
    int k = year % 100, j = year / 100;
    int h = (day + (13 * (month + 1)) / 5 + k + k / 4 + j / 4 - 2 * j) % 7;
    return (h + 5) % 7 + 1;
}

// 检查是否为未来时间
int is_future_time(int year, int month, int day, int hour) {
    int curr_year, curr_month, curr_day, curr_hour;
    get_current_time(&curr_year, &curr_month, &curr_day, &curr_hour);
    
    printf("当前时间：%d年%d月%d日 %d:00\n", curr_year, curr_month, curr_day, curr_hour);
    printf("预约时间：%d年%d月%d日 %d:00\n", year, month, day, hour);
    
    if (year > curr_year) {
        printf("年份比较：预约年份 > 当前年份\n");
        return 1;
    }
    if (year < curr_year) {
        printf("年份比较：预约年份 < 当前年份\n");
        return 0;
    }
    if (month > curr_month) {
        printf("月份比较：预约月份 > 当前月份\n");
        return 1;
    }
    if (month < curr_month) {
        printf("月份比较：预约月份 < 当前月份\n");
        return 0;
    }
    if (day > curr_day) {
        printf("日期比较：预约日期 > 当前日期\n");
        return 1;
    }
    if (day < curr_day) {
        printf("日期比较：预约日期 < 当前日期\n");
        return 0;
    }
    
    printf("小时比较：预约小时(%d) vs 当前小时(%d)\n", hour, curr_hour);
    if (hour > curr_hour) {
        printf("预约时间在未来\n");
        return 1;
    } else {
        printf("预约时间不在未来\n");
        return 0;
    }
}

// 检查场地在指定时间是否开放
int is_court_available_time(int weekday, int hour) {
    printf("检查开放时间：星期%d，%d点\n", weekday, hour);
    
    if (weekday >= 1 && weekday <= 5) { // 工作日
        if (hour < 15 || hour >= 21) {
            printf("不在工作日开放时间内(15-21点)\n");
            return 0;
        }
        if ((weekday == 1 || weekday == 3 || weekday == 5) &&
            hour >= 17 && hour < 20) {
            printf("周一三五17-20点为教工羽协活动时间\n");
            return 0;
        }
        printf("工作日时间可用\n");
        return 1;
    }
    if (weekday == 6 || weekday == 7) { // 周末
        if (hour >= 9 && hour < 21) {
            printf("周末时间可用\n");
            return 1;
        } else {
            printf("不在周末开放时间内(9-21点)\n");
            return 0;
        }
    }
    return 0;
}

int main() {
    int year = 2025, month = 7, day = 1, hour = 16;
    
    printf("=== 调试预约时间验证 ===\n");
    printf("测试预约：%d年%d月%d日 %d:00\n\n", year, month, day, hour);
    
    // 1. 检查星期几
    int weekday = get_weekday(year, month, day);
    char* weekday_names[] = {"", "周一", "周二", "周三", "周四", "周五", "周六", "周日"};
    printf("1. 星期几检查：%d (%s)\n\n", weekday, weekday_names[weekday]);
    
    // 2. 检查是否为未来时间
    printf("2. 未来时间检查：\n");
    int is_future = is_future_time(year, month, day, hour);
    printf("结果：%s\n\n", is_future ? "是未来时间" : "不是未来时间");
    
    // 3. 检查开放时间
    printf("3. 开放时间检查：\n");
    int is_available = is_court_available_time(weekday, hour);
    printf("结果：%s\n\n", is_available ? "在开放时间内" : "不在开放时间内");
    
    // 4. 综合结果
    printf("4. 综合验证结果：\n");
    if (is_future && is_available) {
        printf("✓ 可以预约\n");
    } else {
        printf("✗ 不能预约\n");
        if (!is_future) printf("  - 原因：不是未来时间\n");
        if (!is_available) printf("  - 原因：不在开放时间内\n");
    }
    
    return 0;
}
