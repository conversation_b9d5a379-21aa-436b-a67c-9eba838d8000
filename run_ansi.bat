@echo off
echo 正在设置ANSI编码环境...
chcp 936 >nul

echo 正在编译羽毛球预约系统（ANSI编码）...

REM 尝试使用不同的编译器和参数
if exist "C:\Program Files\Dev-Cpp\MinGW64\bin\gcc.exe" (
    echo 使用Dev-C++编译器...
    "C:\Program Files\Dev-Cpp\MinGW64\bin\gcc.exe" -finput-charset=GBK -fexec-charset=GBK -o badminton_simple badminton_simple.c
    if %errorlevel% neq 0 (
        echo 尝试默认编译参数...
        "C:\Program Files\Dev-Cpp\MinGW64\bin\gcc.exe" -o badminton_simple badminton_simple.c
    )
) else if exist "C:\MinGW\bin\gcc.exe" (
    echo 使用MinGW编译器...
    "C:\MinGW\bin\gcc.exe" -finput-charset=GBK -fexec-charset=GBK -o badminton_simple badminton_simple.c
    if %errorlevel% neq 0 (
        echo 尝试默认编译参数...
        "C:\MinGW\bin\gcc.exe" -o badminton_simple badminton_simple.c
    )
) else (
    where gcc >nul 2>&1
    if %errorlevel% equ 0 (
        echo 使用系统GCC编译器...
        gcc -finput-charset=GBK -fexec-charset=GBK -o badminton_simple badminton_simple.c
        if %errorlevel% neq 0 (
            echo 尝试默认编译参数...
            gcc -o badminton_simple badminton_simple.c
        )
    ) else (
        echo 未找到GCC编译器！
        echo 请使用以下方法之一：
        echo 1. 安装Dev-C++
        echo 2. 在Dev-C++中打开badminton_simple.c文件
        echo 3. 按F9编译运行
        pause
        exit /b 1
    )
)

if %errorlevel% neq 0 (
    echo 编译失败！请检查代码。
    pause
    exit /b 1
)

echo 编译成功！
echo 正在运行程序...
echo.
badminton_simple.exe

echo.
echo 程序运行结束。
pause
