@echo off
title Badminton Court Reservation System

echo ========================================
echo   Badminton Court Reservation System
echo ========================================
echo.

echo Checking for compiled program...

if exist badminton_english.exe (
    echo Found compiled program. Starting...
    echo.
    badminton_english.exe
    goto :end
)

echo Trying to compile...

REM Try different compiler paths
if exist "C:\Program Files\Dev-Cpp\MinGW64\bin\gcc.exe" (
    "C:\Program Files\Dev-Cpp\MinGW64\bin\gcc.exe" -o badminton_english badminton_english.c
) else if exist "C:\MinGW\bin\gcc.exe" (
    "C:\MinGW\bin\gcc.exe" -o badminton_english badminton_english.c
) else (
    where gcc >nul 2>&1
    if %errorlevel% equ 0 (
        gcc -o badminton_english badminton_english.c
    ) else (
        echo No compiler found!
        echo Please:
        echo 1. Install Dev-C++ or Code::Blocks
        echo 2. Open badminton_english.c in your IDE and compile
        echo 3. Then run this batch file again
        goto :end
    )
)

if %errorlevel% neq 0 (
    echo Compilation failed!
    goto :end
)

echo Compilation successful!
echo Starting program...
echo.
badminton_english.exe

:end
echo.
echo Program finished.
pause
