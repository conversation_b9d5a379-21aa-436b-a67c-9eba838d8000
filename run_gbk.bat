@echo off
title 羽毛球场预约系统

echo 正在设置中文编码环境...
chcp 936 >nul

echo.
echo ========================================
echo   羽毛球场预约系统 - 编译运行工具
echo ========================================
echo.

echo 正在检查编译器...

REM 检查是否存在已编译的程序
if exist badminton_gbk.exe (
    echo 发现已编译的程序文件。
    goto :run_program
)

REM 尝试不同的编译器路径
set COMPILER_FOUND=0

if exist "C:\Program Files\Dev-Cpp\MinGW64\bin\gcc.exe" (
    set GCC_PATH="C:\Program Files\Dev-Cpp\MinGW64\bin\gcc.exe"
    set COMPILER_FOUND=1
    echo 找到Dev-C++编译器
) else if exist "C:\MinGW\bin\gcc.exe" (
    set GCC_PATH="C:\MinGW\bin\gcc.exe"
    set COMPILER_FOUND=1
    echo 找到MinGW编译器
) else if exist "C:\TDM-GCC-64\bin\gcc.exe" (
    set GCC_PATH="C:\TDM-GCC-64\bin\gcc.exe"
    set COMPILER_FOUND=1
    echo 找到TDM-GCC编译器
) else (
    where gcc >nul 2>&1
    if %errorlevel% equ 0 (
        set GCC_PATH=gcc
        set COMPILER_FOUND=1
        echo 找到系统PATH中的GCC编译器
    )
)

if %COMPILER_FOUND% equ 0 (
    echo.
    echo 错误：未找到GCC编译器！
    echo.
    echo 请选择以下解决方案之一：
    echo 1. 安装Dev-C++（推荐）
    echo 2. 安装Code::Blocks
    echo 3. 安装MinGW
    echo 4. 在IDE中直接打开badminton_gbk.c文件编译运行
    echo.
    pause
    exit /b 1
)

echo 正在编译程序...
%GCC_PATH% -o badminton_gbk badminton_gbk.c

if %errorlevel% neq 0 (
    echo.
    echo 编译失败！请检查代码是否有错误。
    echo.
    pause
    exit /b 1
)

echo 编译成功！

:run_program
echo.
echo 正在启动羽毛球场预约系统...
echo ========================================
echo.

badminton_gbk.exe

echo.
echo ========================================
echo 程序运行结束。
echo.
pause
