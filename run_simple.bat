@echo off
echo 正在设置中文编码...
chcp 936 >nul

echo 正在编译羽毛球预约系统...
if exist "C:\Program Files\Dev-Cpp\MinGW64\bin\gcc.exe" (
    "C:\Program Files\Dev-Cpp\MinGW64\bin\gcc.exe" -o badminton_simple badminton_simple.c
) else if exist "C:\MinGW\bin\gcc.exe" (
    "C:\MinGW\bin\gcc.exe" -o badminton_simple badminton_simple.c
) else (
    echo 未找到GCC编译器！
    echo 请使用以下方法之一：
    echo 1. 安装Dev-C++或Code::Blocks
    echo 2. 直接在IDE中打开badminton_simple.c文件编译运行
    echo 3. 安装MinGW编译器
    pause
    exit /b 1
)

if %errorlevel% neq 0 (
    echo 编译失败！请检查代码是否有错误。
    pause
    exit /b 1
)

echo 编译成功！
echo 正在启动羽毛球预约系统...
badminton_simple.exe

echo.
echo 程序运行结束。
pause
