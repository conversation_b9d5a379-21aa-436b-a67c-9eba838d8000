@echo off
echo 正在编译修复后的羽毛球预约系统...

if exist "C:\Program Files\Dev-Cpp\MinGW64\bin\gcc.exe" (
    "C:\Program Files\Dev-Cpp\MinGW64\bin\gcc.exe" -o badminton_simple badminton_simple.c
) else if exist "C:\MinGW\bin\gcc.exe" (
    "C:\MinGW\bin\gcc.exe" -o badminton_simple badminton_simple.c
) else (
    where gcc >nul 2>&1
    if %errorlevel% equ 0 (
        gcc -o badminton_simple badminton_simple.c
    ) else (
        echo 未找到GCC编译器！
        echo 请使用Dev-C++或其他IDE编译badminton_simple.c
        pause
        exit /b 1
    )
)

if %errorlevel% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo 编译成功！正在运行程序...
echo.
badminton_simple.exe

echo.
echo 程序运行结束。
pause
