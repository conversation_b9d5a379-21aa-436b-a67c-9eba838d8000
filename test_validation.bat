@echo off
echo 正在编译带验证功能的羽毛球预约系统...

if exist "C:\Program Files\Dev-Cpp\MinGW64\bin\gcc.exe" (
    "C:\Program Files\Dev-Cpp\MinGW64\bin\gcc.exe" -o badminton_simple badminton_simple.c
) else if exist "C:\MinGW\bin\gcc.exe" (
    "C:\MinGW\bin\gcc.exe" -o badminton_simple badminton_simple.c
) else (
    where gcc >nul 2>&1
    if %errorlevel% equ 0 (
        gcc -o badminton_simple badminton_simple.c
    ) else (
        echo 未找到GCC编译器！请使用Dev-C++编译
        pause
        exit /b 1
    )
)

if %errorlevel% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo 编译成功！
echo.
echo 功能说明：
echo - 学号/工号：6-12位数字
echo - 电话号码：11位手机号（以1开头）
echo - 场地编号：1-8
echo - 时间限制：按开放时间规则
echo.
echo 正在运行程序...
badminton_simple.exe

echo.
pause
