羽毛球预约系统 - 中文乱码解决方案
=====================================

问题原因：
文件编码与Windows控制台编码不匹配导致中文显示乱码。

解决方案（按优先级排序）：

方案1：使用记事本重新保存（最简单有效）
----------------------------------------
1. 用记事本打开 badminton_simple.c
2. 点击"文件" -> "另存为"
3. 在保存对话框底部，将"编码"改为"ANSI"
4. 保存文件
5. 重新编译运行

方案2：使用特殊编译参数
----------------------
运行 run_ansi.bat，它会自动尝试使用正确的编码参数编译。

方案3：在IDE中设置编码
---------------------
如果使用Dev-C++：
1. 打开badminton_simple.c
2. 工具 -> 编辑器选项 -> 常规
3. 设置默认编码为"System Default"或"GBK"
4. 重新保存文件并编译

方案4：使用Visual Studio Code
----------------------------
1. 用VS Code打开badminton_simple.c
2. 右下角点击编码格式（通常显示UTF-8）
3. 选择"通过编码重新打开"
4. 选择"GBK"或"GB2312"
5. 重新保存文件

推荐方案：
---------
最简单有效的是方案1，用记事本另存为ANSI编码。
这样可以确保在任何Windows系统上都能正常显示中文。

注意事项：
---------
1. 保存为ANSI编码后，文件大小可能会略有变化
2. 确保编译器支持GBK编码
3. 如果还是有问题，可以考虑使用英文界面版本

测试方法：
---------
编译运行后，如果能正常显示中文菜单，说明问题已解决。
